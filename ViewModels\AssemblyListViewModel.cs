using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Threading;
using System.Threading.Tasks;
using TeklaTool.Models;
using TeklaTool.Services;
using TeklaTool.Utils;

namespace TeklaTool.ViewModels
{
    public class AssemblyListViewModel : ViewModelBase, IDisposable
    {
        private TeklaModelService _teklaModelService;
        private MainViewModel _mainViewModel;
        private readonly AppConfig _config = AppConfig.Instance;
        private readonly ObservableCollection<AssemblyInfo> _assemblies = new ObservableCollection<AssemblyInfo>();
        private readonly ObservableCollection<MergedAssemblyRow> _mergedAssemblies = new ObservableCollection<MergedAssemblyRow>();
        private bool _isMergeRows;
        private bool _disposed = false;

        // 标记是否正在进行高亮操作
        private bool _isHighlighting = false;

        // 缓存相关字段
        private Dictionary<string, List<int>> _assemblyNumberToModelObjectIdsCache = new Dictionary<string, List<int>>();
        private Dictionary<int, List<int>> _assemblyModelObjectIdToPartsCache = new Dictionary<int, List<int>>();

        // 高亮缓存 - 存储构件编号与其对应的所有零件ID
        private Dictionary<string, List<int>> _assemblyHighlightCache = new Dictionary<string, List<int>>();

        // 上一次高亮的构件编号缓存
        private List<string> _lastHighlightedAssemblyNumbers = new List<string>();
        private List<int> _lastHighlightedModelObjectIds = new List<int>();

        // 上一次高亮的零件ID列表
        private List<int> _lastHighlightedPartIds = new List<int>();

        public ObservableCollection<AssemblyInfo> Assemblies => _assemblies;
        public ObservableCollection<MergedAssemblyRow> MergedAssemblies => _mergedAssemblies;

        // 是否正在高亮
        public bool IsHighlighting
        {
            get => _isHighlighting;
            set
            {
                if (SetProperty(ref _isHighlighting, value))
                {
                    // 通知UI更新
                    OnPropertyChanged();
                }
            }
        }
        public bool IsMergeRows
        {
            get => _isMergeRows;
            set
            {
                if (SetProperty(ref _isMergeRows, value))
                {
                    // 使用Task.Run在后台线程执行耗时操作
                    Task.Run(() =>
                    {
                        // 在UI线程上更新UI
                        System.Windows.Application.Current.Dispatcher.Invoke(() =>
                        {
                            // 更新合并行
                            UpdateMergedAssemblies();
                            // 通知视图更新
                            OnPropertyChanged(nameof(AssembliesView));
                        });
                    });
                }
            }
        }
        public IEnumerable<object> AssembliesView => IsMergeRows ? (IEnumerable<object>)MergedAssemblies : Assemblies;
        public AssemblyListViewModel(TeklaModelService service, MainViewModel mainViewModel = null)
        {
            ExceptionHandler.SafeExecute(() =>
            {
                _teklaModelService = service;
                _mainViewModel = mainViewModel;

                // 初始化缓存
                _assemblyNumberToModelObjectIdsCache = new Dictionary<string, List<int>>();
                _assemblyModelObjectIdToPartsCache = new Dictionary<int, List<int>>();
                _assemblyHighlightCache = new Dictionary<string, List<int>>();

                Assemblies.CollectionChanged += (s, e) =>
                {
                    if (IsMergeRows && !_disposed) UpdateMergedAssemblies();

                    // 当集合变化时，清空缓存
                    if (e.Action == System.Collections.Specialized.NotifyCollectionChangedAction.Reset)
                    {
                        _assemblyNumberToModelObjectIdsCache.Clear();
                        _assemblyModelObjectIdToPartsCache.Clear();
                        _assemblyHighlightCache.Clear();
                    }
                };
                Logger.LogInfo("AssemblyListViewModel初始化完成");
            }, "初始化AssemblyListViewModel");
        }
        public async void SetAssemblies(IEnumerable<AssemblyInfo> assemblies)
        {
            if (_disposed) return;

            await ExceptionHandler.HandleTeklaOperation(async () =>
            {
                // 清空缓存
                _assemblyNumberToModelObjectIdsCache?.Clear();
                _assemblyModelObjectIdToPartsCache?.Clear();
                _assemblyHighlightCache?.Clear();

                // 在后台线程处理数据
                var assemblyList = assemblies?.ToList() ?? new List<AssemblyInfo>(); // 避免多次枚举

                // 在UI线程中安全地更新集合
                Application.Current.Dispatcher.Invoke(() =>
                {
                    if (_disposed) return;

                    // 清空当前集合
                    Assemblies.Clear();

                    // 批量添加数据
                    foreach (var assembly in assemblyList)
                    {
                        if (assembly != null && !_disposed)
                        {
                            Assemblies.Add(assembly);
                        }
                    }
                });

                // 更新合并行
                if (IsMergeRows && !_disposed) UpdateMergedAssemblies();

                // 在后台预加载构件零件信息
                if (Assemblies.Count > 0 && !_disposed)
                {
                    TryGetAssemblyParts();
                }

                Logger.LogInfo($"设置装配体数据完成，共 {Assemblies.Count} 个装配体");
            }, "设置装配体数据");
        }

        private async void UpdateMergedAssemblies()
        {
            if (_disposed || !IsMergeRows) return;

            // 如果没有构件数据，直接返回
            if (Assemblies == null || Assemblies.Count == 0)
            {
                MergedAssemblies.Clear();
                OnPropertyChanged(nameof(AssembliesView));
                return;
            }

            await ExceptionHandler.HandleTeklaOperation(async () =>
            {
                // 在UI线程创建集合的副本，避免并发修改异常
                List<AssemblyInfo> assemblySnapshot;
                try
                {
                    assemblySnapshot = Assemblies.ToList();
                }
                catch (InvalidOperationException)
                {
                    // 如果集合正在被修改，稍后重试
                    await Task.Delay(100);
                    if (_disposed || !IsMergeRows) return;
                    try
                    {
                        assemblySnapshot = Assemblies.ToList();
                    }
                    catch
                    {
                        // 如果仍然失败，直接返回
                        Logger.LogWarning("无法创建构件集合快照，跳过合并操作");
                        return;
                    }
                }

                // 在后台线程处理数据
                var mergedRows = await Task.Run(() =>
                {
                    if (_disposed) return new List<MergedAssemblyRow>();

                    try
                    {
                        // 按构件编号进行分组，因为构件编号相同的必定一致
                        return assemblySnapshot
                            .Where(a => !string.IsNullOrEmpty(a.AssemblyNumber)) // 过滤掉空的构件编号
                            .GroupBy(a => a.AssemblyNumber) // 只按构件编号分组
                            .Select((g, idx) => new MergedAssemblyRow
                            {
                                Index = idx + 1,
                                AssemblyNumber = g.Key,
                                Name = g.First().Name,
                                Profile = g.First().Profile,
                                Material = g.First().Material,
                                Finish = g.First().Finish,
                                Class = g.First().Class,
                                Phase = g.First().Phase,
                                Count = g.Count(), // 相同构件编号的数量
                                PartCount = g.Sum(a => a.PartCount), // 所有相同构件的零件总数
                                AssemblyPrefix = g.First().AssemblyPrefix,
                                AssemblyStartNumber = g.First().AssemblyStartNumber,
                                Guid = g.First().Guid, // 使用第一个构件的GUID
                                ModelObjectIds = g.Select(a => a.ModelObjectId).Where(id => id > 0).Distinct().ToList(), // 去重并过滤无效ID
                                Remark = string.Join("; ", g.Select(a => a.Remark).Where(r => !string.IsNullOrEmpty(r)).Distinct()) // 合并所有非空备注
                            })
                            .OrderBy(r => r.AssemblyNumber) // 按构件编号排序
                            .ToList();
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError($"处理合并数据时发生错误: {ex.Message}");
                        return new List<MergedAssemblyRow>();
                    }
                });

                if (_disposed) return;

                // 在UI线程更新集合
                MergedAssemblies.Clear();
                foreach (var row in mergedRows)
                {
                    MergedAssemblies.Add(row);
                }

                // 通知视图更新
                OnPropertyChanged(nameof(AssembliesView));
                Logger.LogInfo($"更新装配体合并行完成，共 {MergedAssemblies.Count} 行，原始构件数 {assemblySnapshot.Count}");
            }, "更新装配体合并行");
        }

        public void HandleAssemblySelectionChanged(IList<AssemblyInfo> selectedAssemblies)
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                // 直接高亮，不使用防抖动
                if (_mainViewModel != null && !_mainViewModel.EnableHighlight)
                {
                    return; // 如果禁用了高亮功能，直接返回
                }

                if (selectedAssemblies == null || selectedAssemblies.Count == 0)
                {
                    // 如果没有选中任何构件，清除高亮
                    _teklaModelService?.HighlightObjects(new List<int>());
                    _lastHighlightedAssemblyNumbers?.Clear();
                    _lastHighlightedModelObjectIds?.Clear();
                    _lastHighlightedPartIds?.Clear();
                    return;
                }

                // 获取构件编号列表
                var assemblyNumbers = selectedAssemblies.Select(a => a.AssemblyNumber).ToList();

                // 更新最后高亮的构件编号
                _lastHighlightedAssemblyNumbers = assemblyNumbers;

                // 使用构件整体选择方式（默认方式）
                _teklaModelService?.SelectAssembliesAsWhole(assemblyNumbers);
                Logger.LogInfo($"选择了 {assemblyNumbers.Count} 个构件作为整体");
            }, "处理装配体选择变更");
        }

        /// <summary>
        /// 处理合并行的选择变更
        /// </summary>
        /// <param name="selectedMergedRows">选中的合并行</param>
        public void HandleMergedAssemblySelectionChanged(IList<MergedAssemblyRow> selectedMergedRows)
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                // 检查是否启用了高亮功能
                if (_mainViewModel != null && !_mainViewModel.EnableHighlight)
                {
                    return; // 如果禁用了高亮功能，直接返回
                }

                if (selectedMergedRows == null || selectedMergedRows.Count == 0)
                {
                    // 如果没有选中任何合并行，清除高亮
                    _teklaModelService?.HighlightObjects(new List<int>());
                    _lastHighlightedAssemblyNumbers?.Clear();
                    _lastHighlightedModelObjectIds?.Clear();
                    _lastHighlightedPartIds?.Clear();
                    return;
                }

                // 收集所有选中合并行的构件编号
                var assemblyNumbers = new List<string>();
                foreach (var mergedRow in selectedMergedRows)
                {
                    if (!string.IsNullOrEmpty(mergedRow.AssemblyNumber))
                    {
                        assemblyNumbers.Add(mergedRow.AssemblyNumber);
                    }
                }

                // 去重
                assemblyNumbers = assemblyNumbers.Distinct().ToList();

                if (assemblyNumbers.Count == 0)
                {
                    return;
                }

                // 根据构件编号从原始数据中找到所有匹配的构件
                var allMatchingAssemblies = Assemblies
                    .Where(a => assemblyNumbers.Contains(a.AssemblyNumber))
                    .ToList();

                // 收集所有模型对象ID
                var allModelObjectIds = allMatchingAssemblies
                    .Select(a => a.ModelObjectId)
                    .Where(id => id > 0)
                    .Distinct()
                    .ToList();

                // 更新最后高亮的信息
                _lastHighlightedAssemblyNumbers = assemblyNumbers;
                _lastHighlightedModelObjectIds = allModelObjectIds;

                // 使用构件整体选择方式（默认方式）
                _teklaModelService?.SelectAssembliesAsWhole(assemblyNumbers);
                Logger.LogInfo($"选择了 {selectedMergedRows.Count} 个合并行中的 {assemblyNumbers.Count} 个构件作为整体");
            }, "处理合并装配体选择变更");
        }



        // 更新缓存的方法
        private void UpdateCache(string assemblyNumber, List<int> modelObjectIds)
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                if (!string.IsNullOrEmpty(assemblyNumber) && modelObjectIds != null && modelObjectIds.Count > 0)
                {
                    // 更新构件编号到模型对象ID的缓存
                    _assemblyNumberToModelObjectIdsCache[assemblyNumber] = modelObjectIds;
                }
            }, "更新装配体缓存");
        }

        // 更新所有缓存的方法
        private void UpdateCache()
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                // 更新构件编号到模型对象ID的缓存
                _assemblyNumberToModelObjectIdsCache?.Clear();
                foreach (var assembly in Assemblies)
                {
                    if (_disposed) return;

                    if (!_assemblyNumberToModelObjectIdsCache.ContainsKey(assembly.AssemblyNumber))
                    {
                        _assemblyNumberToModelObjectIdsCache[assembly.AssemblyNumber] = new List<int>();
                    }
                    _assemblyNumberToModelObjectIdsCache[assembly.AssemblyNumber].Add(assembly.ModelObjectId);
                }
                Logger.LogInfo($"更新装配体缓存完成，共 {_assemblyNumberToModelObjectIdsCache.Count} 个装配体编号");
            }, "更新装配体缓存");
        }

        // 尝试获取构件中的所有零件ID
        private async void TryGetAssemblyParts()
        {
            if (_disposed) return;

            await ExceptionHandler.HandleTeklaOperation(async () =>
            {
                // 这个方法可以在应用启动时或模型加载后调用
                // 它会尝试获取所有构件中的零件，并更新缓存

                // 由于这个操作可能比较耗时，在后台线程中执行
                await Task.Run(async () =>
                {
                    if (_disposed) return;

                    // 使用字典记录已处理的构件编号，避免重复处理
                    var processedAssemblyNumbers = new HashSet<string>();

                    // 分批处理构件，避免长时间阻塞
                    int batchSize = Math.Max(1, _config.MaxConcurrentOperations / 2); // 使用配置的1/2作为批次大小
                    var assemblyGroups = Assemblies
                        .GroupBy(a => a.AssemblyNumber)
                        .Select(g => new { AssemblyNumber = g.Key, ModelObjectIds = g.Select(a => a.ModelObjectId).ToList() })
                        .ToList();

                    for (int i = 0; i < assemblyGroups.Count; i += batchSize)
                    {
                        if (_disposed) return;

                        var batch = assemblyGroups.Skip(i).Take(batchSize).ToList();

                        foreach (var group in batch)
                        {
                            if (_disposed) return;

                            if (!processedAssemblyNumbers.Contains(group.AssemblyNumber) &&
                                !_assemblyNumberToModelObjectIdsCache.ContainsKey(group.AssemblyNumber))
                            {
                                // 更新缓存
                                UpdateCache(group.AssemblyNumber, group.ModelObjectIds);
                                processedAssemblyNumbers.Add(group.AssemblyNumber);

                                // 预加载构件的零件ID到高亮缓存
                                if (!_assemblyHighlightCache.ContainsKey(group.AssemblyNumber))
                                {
                                    try
                                    {
                                        // 尝试获取构件中的所有零件ID
                                        var partIds = _teklaModelService?.GetPartIdsForAssembly(group.AssemblyNumber) ?? new List<int>();
                                        if (partIds.Count > 0)
                                        {
                                            _assemblyHighlightCache[group.AssemblyNumber] = partIds;
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"预加载构件 {group.AssemblyNumber} 的零件ID时出错: {ex.Message}");
                                    }
                                }
                            }
                        }

                        // 每处理一批，短暂暂停，避免占用过多资源
                        await Task.Delay(50);
                    }

                    Logger.LogInfo($"预加载完成，共处理 {processedAssemblyNumbers.Count} 个不同构件");
                });
            }, "预加载装配体零件信息");
        }

        // 搜索方法
        public void Search(string searchText)
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                if (string.IsNullOrWhiteSpace(searchText))
                {
                    // 如果搜索文本为空，显示所有装配体
                    OnPropertyChanged(nameof(AssembliesView));
                    return;
                }

                // 这里可以实现具体的搜索逻辑
                // 例如：筛选包含搜索文本的装配体
                Logger.LogInfo($"在装配体列表中搜索: {searchText}");
            }, "搜索装配体");
        }

        #region IDisposable Implementation

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    ExceptionHandler.SafeExecute(() =>
                    {
                        _assemblies?.Clear();
                        _mergedAssemblies?.Clear();
                        _assemblyNumberToModelObjectIdsCache?.Clear();
                        _assemblyModelObjectIdToPartsCache?.Clear();
                        _assemblyHighlightCache?.Clear();
                        _lastHighlightedAssemblyNumbers?.Clear();
                        _lastHighlightedModelObjectIds?.Clear();
                        _lastHighlightedPartIds?.Clear();
                        Logger.LogInfo("AssemblyListViewModel资源已释放");
                    }, "释放AssemblyListViewModel资源");
                }

                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~AssemblyListViewModel()
        {
            Dispose(false);
        }

        #endregion

        public class MergedAssemblyRow
        {
            public int Index { get; set; }
            public string AssemblyNumber { get; set; }
            public string Name { get; set; }
            public string Profile { get; set; }
            public string Material { get; set; }
            public string Finish { get; set; }
            public string Class { get; set; }
            public string Phase { get; set; }
            public int Count { get; set; }
            public int PartCount { get; set; }
            public string AssemblyPrefix { get; set; }
            public string AssemblyStartNumber { get; set; }
            public string Guid { get; set; }
            public List<int> ModelObjectIds { get; set; }
            public string Remark { get; set; }
        }
    }
}
