using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Collections;
using Tekla.Structures.Model;
using Tekla.Structures.Model.UI;
using Tekla.Structures.Filtering;
using Tekla.Structures.Filtering.Categories;
using TeklaTool.Models;
using TeklaTool.Utils;

namespace TeklaTool.Services
{
    public class TeklaModelService : IDisposable
    {
        private Model _model;
        private Dictionary<string, List<TeklaModelPart>> _assemblyCache;
        private bool _useMockData;
        private List<TeklaModelPart> _mockParts;
        private bool _disposed = false;

        // 缓存相关字段
        private List<TeklaModelPart> _partsCache;
        private DateTime _partsCacheTimestamp = DateTime.MinValue;
        private readonly AppConfig _config = AppConfig.Instance;

        private Dictionary<string, List<int>> _assemblyPartIdsCache = new Dictionary<string, List<int>>();

        public TeklaModelService()
        {
            // 使用改进的异常处理
            ExceptionHandler.HandleTeklaOperation(() =>
            {
                _model = new Model();
                _assemblyCache = new Dictionary<string, List<TeklaModelPart>>();
                _useMockData = false;
                _mockParts = new List<TeklaModelPart>();
                _assemblyPartIdsCache = new Dictionary<string, List<int>>();

                // 尝试初始化连接，如果失败则记录错误
                if (!_model.GetConnectionStatus())
                {
                    Logger.LogError("无法连接到Tekla模型，请确保Tekla Structures已启动并打开了模型");
                }
                else
                {
                    Logger.LogInfo("成功连接到Tekla模型");
                }
            }, "初始化Tekla模型服务");

            // 确保关键字段被初始化
            _assemblyCache ??= new Dictionary<string, List<TeklaModelPart>>();
            _mockParts ??= new List<TeklaModelPart>();
            _assemblyPartIdsCache ??= new Dictionary<string, List<int>>();
        }

        public bool GetConnectionStatus()
        {
            if (_disposed) return false;

            return ExceptionHandler.HandleTeklaOperation(() =>
            {
                return _model?.GetConnectionStatus() ?? false;
            }, "获取连接状态", false);
        }

        public List<TeklaModelPart> GetAllParts(bool useCache = true)
        {
            if (_disposed) return new List<TeklaModelPart>();

            if (_useMockData)
            {
                return _mockParts ?? new List<TeklaModelPart>();
            }

            // 如果启用缓存且缓存有效，则使用缓存
            if (useCache && _config.EnableDataCache && _partsCache != null &&
                _partsCacheTimestamp.Add(_config.CacheDuration) > DateTime.Now)
            {
                Logger.LogInfo($"使用缓存的零件数据，共 {_partsCache.Count} 个零件");
                return _partsCache;
            }

            var parts = ExceptionHandler.HandleTeklaOperation(() =>
            {
                return GetModelParts(false);
            }, "获取所有零件", new List<TeklaModelPart>());

            // 更新缓存
            if (useCache && _config.EnableDataCache && parts.Count <= _config.MaxCacheSize)
            {
                _partsCache = parts;
                _partsCacheTimestamp = DateTime.Now;
                Logger.LogInfo($"已更新零件缓存，共 {parts.Count} 个零件");
            }

            return parts;
        }

        /// <summary>
        /// 清理缓存
        /// </summary>
        public void ClearCache()
        {
            ExceptionHandler.SafeExecute(() =>
            {
                _partsCache?.Clear();
                _partsCache = null;
                _assemblyCache?.Clear();
                _assemblyPartIdsCache?.Clear();
                _partsCacheTimestamp = DateTime.MinValue;
                Logger.LogInfo("已清理所有缓存");
            }, "清理缓存");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    ExceptionHandler.SafeExecute(() =>
                    {
                        ClearCache();
                        _mockParts?.Clear();
                        Logger.LogInfo("TeklaModelService资源已释放");
                    }, "释放TeklaModelService资源");
                }
                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~TeklaModelService()
        {
            Dispose(false);
        }

        public List<TeklaModelPart> GetSelectedParts()
        {
            // 选中的零件不使用缓存
            return GetModelParts(true);
        }



        public List<TeklaModelPart> GetModelParts(bool selectedOnly = false)
        {
            if (_useMockData)
            {
                return _mockParts;
            }

            List<TeklaModelPart> parts = new List<TeklaModelPart>();
            try
            {
                if (!_model.GetConnectionStatus())
                {
                    LogError("未连接到Tekla模型");
                    return parts;
                }

                DateTime startTime = DateTime.Now;
                int index = 0;
                int processedCount = 0;

                if (selectedOnly)
                {
                    // 使用UI命名空间的ModelObjectSelector获取选中对象
                    ModelObjectEnumerator objectEnum = new Tekla.Structures.Model.UI.ModelObjectSelector().GetSelectedObjects();

                    // 记录选中对象的数量
                    int selectedCount = 0;
                    ModelObjectEnumerator countEnum = objectEnum.GetEnumerator() as ModelObjectEnumerator;
                    while (countEnum != null && countEnum.MoveNext())
                    {
                        selectedCount++;
                    }
                    LogInfo($"用户选中了 {selectedCount} 个对象");

                    // 重新获取选中对象
                    objectEnum = new Tekla.Structures.Model.UI.ModelObjectSelector().GetSelectedObjects();

                    // 处理选中的对象
                    while (objectEnum.MoveNext())
                    {
                        if (objectEnum.Current is Part part)
                        {
                            var partInfo = GetPartInfo(part, index + 1);
                            if (partInfo != null)
                            {
                                parts.Add(partInfo);
                                index++;
                            }
                            processedCount++;
                        }
                    }
                }
                else
                {
                    // 优化：使用过滤器获取所有类型的零件
                    LogInfo("使用过滤器获取所有零件，包括直梁、折梁和钢板...");
                    Tekla.Structures.Model.ModelObjectSelector selector = _model.GetModelObjectSelector();

                    // 获取主要零件类型
                    // 直梁
                    LogInfo("开始处理直梁...");
                    ModelObjectEnumerator beamEnum = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.BEAM);
                    ProcessParts(parts, beamEnum, ref index, ref processedCount);

                    // 折梁
                    LogInfo("开始处理折梁...");
                    ModelObjectEnumerator polyBeamEnum = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.POLYBEAM);
                    ProcessParts(parts, polyBeamEnum, ref index, ref processedCount);

                    // 钢板
                    LogInfo("开始处理钢板...");
                    ModelObjectEnumerator contourPlateEnum = selector.GetAllObjectsWithType(ModelObject.ModelObjectEnum.CONTOURPLATE);
                    ProcessParts(parts, contourPlateEnum, ref index, ref processedCount);
                }

                LogInfo($"获取了 {parts.Count} 个零件信息，处理了 {processedCount} 个对象，耗时 {(DateTime.Now - startTime).TotalSeconds:F1} 秒");
                return parts;
            }
            catch (Exception ex)
            {
                LogError("获取零件列表时发生错误: " + ex.Message + (ex.InnerException != null ? ", " + ex.InnerException.Message : ""));
                return parts;
            }
        }

        /// <summary>
        /// 处理零件并添加到列表中
        /// </summary>
        private void ProcessParts(List<TeklaModelPart> parts, ModelObjectEnumerator enumerator, ref int index, ref int processedCount)
        {
            DateTime startTime = DateTime.Now;
            int count = 0;

            while (enumerator.MoveNext())
            {
                if (enumerator.Current is Part part)
                {
                    var partInfo = GetPartInfo(part, index + 1);
                    if (partInfo != null)
                    {
                        parts.Add(partInfo);
                        index++;
                    }
                    processedCount++;
                    count++;
                }
            }

            LogInfo($"处理了 {count} 个对象，耗时 {(DateTime.Now - startTime).TotalSeconds:F1} 秒");
        }

        private TeklaModelPart GetPartInfo(Part part, int index)
        {
            try
            {
                // 获取基本属性
                string name = part.Name;
                string profileString = part.Profile.ProfileString;
                string materialString = part.Material.MaterialString;
                string finish = part.Finish;
                string classValue = part.Class;

                // 创建一个字典来存储所有需要获取的属性
                Dictionary<string, string> properties = new Dictionary<string, string>
                {
                    { "PART_POS", string.Empty },
                    { "PART_PREFIX", string.Empty },
                    { "PART_START_NUMBER", string.Empty },
                    { "ASSEMBLY_POS", string.Empty },
                    { "ASSEMBLY_PREFIX", string.Empty },
                    { "ASSEMBLY_START_NUMBER", string.Empty },
                    { "BOUGHT_ITEM", string.Empty },
                    { "COMMENT", string.Empty }
                };

                // 批量获取属性
                foreach (var key in properties.Keys.ToList())
                {
                    string value = string.Empty;
                    part.GetReportProperty(key, ref value);
                    properties[key] = value;
                }

                // 获取Phase信息
                part.GetPhase(out Phase phase);
                string phaseStr = phase?.PhaseNumber.ToString() ?? string.Empty;

                // 检查是否是主零件
                bool isMainPart = false;
                Assembly assembly = part.GetAssembly();
                if (assembly != null)
                {
                    ModelObject mainPart = assembly.GetMainPart();
                    if (mainPart != null && mainPart is Part && mainPart.Identifier.ID == part.Identifier.ID)
                    {
                        isMainPart = true;
                    }
                }

                // 获取GUID和ID
                string guid = part.Identifier.GUID.ToString();
                int id = part.Identifier.ID;

                // 获取螺栓数量
                int boltCount = GetBoltCount(part);

                // 处理买入项
                bool isBoughtItem = !string.IsNullOrEmpty(properties["BOUGHT_ITEM"]) &&
                                   properties["BOUGHT_ITEM"].ToUpper() == "YES";

                return new TeklaModelPart(
                    index,
                    name,
                    properties["PART_POS"],
                    profileString,
                    materialString,
                    finish,
                    classValue,
                    phaseStr,
                    isBoughtItem ? "Yes" : "No",
                    boltCount,
                    properties["ASSEMBLY_POS"],
                    properties["ASSEMBLY_PREFIX"],
                    properties["ASSEMBLY_START_NUMBER"],
                    properties["PART_PREFIX"],
                    properties["PART_START_NUMBER"],
                    guid,
                    id,
                    isMainPart,
                    properties["COMMENT"] ?? string.Empty);
            }
            catch (Exception ex)
            {
                LogError("获取零件信息时发生错误: " + ex.Message);
                return null;
            }
        }

        private int GetBoltCount(Part part)
        {
            int boltCount = 0;
            try
            {
                // 获取与零件相关的螺栓
                ModelObjectEnumerator boltEnum = part.GetBolts();
                while (boltEnum.MoveNext())
                {
                    if (boltEnum.Current is BoltArray boltArray)
                    {
                        boltCount += boltArray.BoltPositions.Count;
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("获取螺栓数量时发生错误: " + ex.Message);
            }
            return boltCount;
        }

        public void LogError(string message) => Logger.LogError(message);

        public void LogInfo(string message) => Logger.LogInfo(message);

        /// <summary>
        /// 高亮显示指定的模型对象
        /// </summary>
        /// <param name="modelObjectIds">模型对象ID列表</param>
        /// <returns>是否成功高亮显示</returns>
        public bool HighlightObjects(List<int> modelObjectIds)
        {
            try
            {
                if (!_model.GetConnectionStatus())
                {
                    LogError("未连接到Tekla模型");
                    return false;
                }

                // 创建ModelObjectSelector对象
                var modelObjectSelector = new Tekla.Structures.Model.UI.ModelObjectSelector();

                // 如果没有指定零件ID，清除当前选择
                if (modelObjectIds == null || modelObjectIds.Count == 0)
                {
                    modelObjectSelector.Select(new System.Collections.ArrayList());
                    _model.CommitChanges();
                    return true;
                }

                LogInfo($"开始高亮显示 {modelObjectIds.Count} 个零件");
                DateTime startTime = DateTime.Now;

                // 收集需要选择的对象
                System.Collections.ArrayList objectsToSelect = new System.Collections.ArrayList();

                // 优化：批量获取对象，而不是逐个获取
                // 将ID列表分批处理，每批最多处理1000个ID，避免一次处理过多导致性能问题
                const int batchSize = 1000;
                for (int i = 0; i < modelObjectIds.Count; i += batchSize)
                {
                    int currentBatchSize = Math.Min(batchSize, modelObjectIds.Count - i);
                    var currentBatch = modelObjectIds.GetRange(i, currentBatchSize);

                    foreach (int id in currentBatch)
                    {
                        try
                        {
                            var modelObject = _model.SelectModelObject(new Tekla.Structures.Identifier(id));
                            if (modelObject != null)
                            {
                                objectsToSelect.Add(modelObject);
                            }
                        }
                        catch (Exception ex)
                        {
                            LogError($"获取对象时发生错误 (ID: {id}): {ex.Message}");
                        }
                    }

                    // 避免频繁记录日志
                    if (i % 5000 == 0 || i + currentBatchSize >= modelObjectIds.Count)
                    {
                        LogInfo($"已处理 {i + currentBatchSize}/{modelObjectIds.Count} 个零件，找到 {objectsToSelect.Count} 个对象");
                    }
                }

                LogInfo($"找到 {objectsToSelect.Count} 个可用对象，耗时 {(DateTime.Now - startTime).TotalSeconds:F1} 秒");

                // 选择找到的对象
                if (objectsToSelect.Count > 0)
                {
                    try
                    {
                        modelObjectSelector.Select(objectsToSelect);
                        _model.CommitChanges(); // 确保选择生效
                        LogInfo($"已高亮显示 {objectsToSelect.Count} 个零件");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        LogError($"选择对象时发生错误: {ex.Message}");
                        return false;
                    }
                }
                else
                {
                    LogError($"未找到指定的零件");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogError($"高亮显示模型对象时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 根据零件列表生成构件信息列表（每个零件对应一个构件信息，用于详细视图）
        /// </summary>
        /// <param name="parts">零件列表</param>
        /// <returns>构件信息列表</returns>
        public List<AssemblyInfo> GetAssemblies(List<TeklaModelPart> parts)
        {
            if (parts == null || parts.Count == 0) return new List<AssemblyInfo>();

            // 每个零件对应一个构件信息，不进行分组
            // 这样可以让合并功能正确工作：
            // - 关闭合并：显示所有构件信息（每个零件对应一行）
            // - 开启合并：按构件编号分组显示
            var assemblies = parts.Select((part, idx) => new AssemblyInfo
            {
                Index = idx + 1,
                AssemblyNumber = part.AssemblyNumber,
                Name = part.Name,
                Profile = part.Profile,
                Material = part.Material,
                Finish = part.Finish,
                Class = part.Class,
                Phase = part.Phase,
                Count = 1, // 每个构件信息对应一个构件实例（固定为1）
                PartCount = 1, // 每个构件信息对应一个零件（固定为1）
                AssemblyPrefix = part.AssemblyPrefix,
                AssemblyStartNumber = part.AssemblyStartNumber,
                Guid = part.Guid,
                ModelObjectId = part.ModelObjectId.HasValue ? part.ModelObjectId.Value : 0,
                Remark = part.Remark
            }).ToList();

            return assemblies;
        }

        /// <summary>
        /// 获取指定构件编号的所有零件ID
        /// </summary>
        /// <param name="assemblyNumber">构件编号</param>
        /// <returns>零件ID列表</returns>
        public List<int> GetPartIdsForAssembly(string assemblyNumber)
        {
            if (string.IsNullOrEmpty(assemblyNumber))
                return new List<int>();

            try
            {
                if (!_model.GetConnectionStatus())
                {
                    LogError("未连接到Tekla模型");
                    return new List<int>();
                }

                List<int> partIds = new List<int>();

                // 使用过滤器查找构件中的零件
                ModelObjectEnumerator partsInAssembly = _model.GetModelObjectSelector().GetObjectsByFilterName("ASSEMBLY." + assemblyNumber);

                while (partsInAssembly.MoveNext())
                {
                    if (partsInAssembly.Current is Part part)
                    {
                        partIds.Add(part.Identifier.ID);
                    }
                }

                // 如果过滤器方法没有找到任何零件，尝试使用遍历方法
                if (partIds.Count == 0)
                {
                    // 创建过滤表达式
                    BinaryFilterExpressionCollection expressionCollection = new BinaryFilterExpressionCollection();

                    // 创建装配位置过滤表达式
                    Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.PositionNumber positionFilter =
                        new Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.PositionNumber();
                    StringConstantFilterExpression positionValue = new StringConstantFilterExpression(assemblyNumber);

                    // 创建二元过滤表达式 (装配位置 == 指定值)
                    BinaryFilterExpression binaryExpression = new BinaryFilterExpression(
                        positionFilter,
                        StringOperatorType.IS_EQUAL,
                        positionValue);

                    expressionCollection.Add(new BinaryFilterExpressionItem(binaryExpression, BinaryFilterOperatorType.BOOLEAN_AND));

                    // 使用过滤器获取零件
                    ModelObjectEnumerator filteredObjects = _model.GetModelObjectSelector().GetObjectsByFilter(expressionCollection);

                    while (filteredObjects.MoveNext())
                    {
                        if (filteredObjects.Current is Part part)
                        {
                            partIds.Add(part.Identifier.ID);
                        }
                    }
                }

                return partIds;
            }
            catch (Exception ex)
            {
                LogError($"获取构件 {assemblyNumber} 的零件ID时发生错误: {ex.Message}");
                return new List<int>();
            }
        }

        /// <summary>
        /// 获取多个构件编号的所有零件ID
        /// </summary>
        /// <param name="assemblyNumbers">构件编号列表</param>
        /// <returns>构件编号到零件ID列表的字典</returns>
        public Dictionary<string, List<int>> GetPartIdsForAssemblies(List<string> assemblyNumbers)
        {
            if (assemblyNumbers == null || assemblyNumbers.Count == 0)
                return new Dictionary<string, List<int>>();

            var result = new Dictionary<string, List<int>>();

            try
            {
                if (!_model.GetConnectionStatus())
                {
                    LogError("未连接到Tekla模型");
                    return result;
                }

                // 创建过滤表达式集合
                BinaryFilterExpressionCollection expressionCollection = new BinaryFilterExpressionCollection();

                // 收集未缓存的构件编号
                var uncachedAssemblyNumbers = new List<string>();

                // 首先检查缓存
                var cachedResults = new Dictionary<string, List<int>>();
                foreach (var assemblyNumber in assemblyNumbers)
                {
                    // 检查是否已有缓存
                    if (_assemblyPartIdsCache.TryGetValue(assemblyNumber, out var partIds))
                    {
                        cachedResults[assemblyNumber] = partIds;
                    }
                    else
                    {
                        uncachedAssemblyNumbers.Add(assemblyNumber);
                    }
                }

                // 如果所有构件都已缓存，直接返回缓存结果
                if (uncachedAssemblyNumbers.Count == 0)
                {
                    return cachedResults;
                }

                // 对每个未缓存的构件位置编号创建一个表达式并添加到集合中
                for (int i = 0; i < uncachedAssemblyNumbers.Count; i++)
                {
                    string assemblyPos = uncachedAssemblyNumbers[i];

                    // 创建装配位置过滤表达式
                    Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.PositionNumber positionFilter =
                        new Tekla.Structures.Filtering.Categories.AssemblyFilterExpressions.PositionNumber();
                    StringConstantFilterExpression positionValue = new StringConstantFilterExpression(assemblyPos);

                    // 创建二元过滤表达式 (装配位置 == 指定值)
                    BinaryFilterExpression binaryExpression = new BinaryFilterExpression(
                        positionFilter,
                        StringOperatorType.IS_EQUAL,
                        positionValue);

                    // 添加到表达式集合，除了最后一个表达式外，其他都用OR运算符连接
                    BinaryFilterOperatorType operatorType =
                        (i < uncachedAssemblyNumbers.Count - 1) ? BinaryFilterOperatorType.BOOLEAN_OR : BinaryFilterOperatorType.BOOLEAN_AND;

                    expressionCollection.Add(new BinaryFilterExpressionItem(binaryExpression, operatorType));
                }

                // 使用过滤器获取零件
                ModelObjectEnumerator filteredObjects = _model.GetModelObjectSelector().GetObjectsByFilter(expressionCollection);

                // 临时存储每个构件的零件ID
                var tempResult = new Dictionary<string, List<int>>();

                while (filteredObjects.MoveNext())
                {
                    if (filteredObjects.Current is Part part)
                    {
                        // 获取构件编号
                        string assemblyMark = string.Empty;
                        part.GetReportProperty("ASSEMBLY_POS", ref assemblyMark);

                        if (!string.IsNullOrEmpty(assemblyMark) && uncachedAssemblyNumbers.Contains(assemblyMark))
                        {
                            if (!tempResult.ContainsKey(assemblyMark))
                            {
                                tempResult[assemblyMark] = new List<int>();
                            }

                            tempResult[assemblyMark].Add(part.Identifier.ID);
                        }
                    }
                }

                // 更新缓存
                foreach (var pair in tempResult)
                {
                    _assemblyPartIdsCache[pair.Key] = pair.Value;
                }

                // 合并缓存结果和新查询结果
                foreach (var pair in cachedResults)
                {
                    result[pair.Key] = pair.Value;
                }

                foreach (var pair in tempResult)
                {
                    result[pair.Key] = pair.Value;
                }

                return result;
            }
            catch (Exception ex)
            {
                LogError($"批量获取构件零件ID时发生错误: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 批量高亮对象，使用缓冲区和分批处理机制优化性能
        /// </summary>
        /// <param name="objectIds">需要高亮的对象ID列表</param>
        /// <param name="batchSize">每批处理的最大对象数量，默认为200</param>
        public void HighlightObjectsBatched(List<int> objectIds, int batchSize = 200)
        {
            if (objectIds == null || objectIds.Count == 0)
            {
                // 清除高亮
                var selector = new Tekla.Structures.Model.UI.ModelObjectSelector();
                selector.Select(new ArrayList());
                return;
            }

            try
            {
                var model = new Model();
                var selector = new Tekla.Structures.Model.UI.ModelObjectSelector();
                var objectsToHighlight = new ArrayList();

                LogInfo($"开始批量高亮 {objectIds.Count} 个对象");

                // 分批处理对象
                for (int i = 0; i < objectIds.Count; i += batchSize)
                {
                    // 清空当前批次的对象列表
                    objectsToHighlight.Clear();

                    // 计算当前批次的结束索引
                    int endIndex = Math.Min(i + batchSize, objectIds.Count);

                    // 获取当前批次的对象
                    for (int j = i; j < endIndex; j++)
                    {
                        try
                        {
                            var modelObject = model.SelectModelObject(new Tekla.Structures.Identifier(objectIds[j]));
                            if (modelObject != null)
                            {
                                objectsToHighlight.Add(modelObject);
                            }
                        }
                        catch (Exception ex)
                        {
                            LogError($"获取对象时发生错误 (ID: {objectIds[j]}): {ex.Message}");
                        }
                    }

                    // 高亮当前批次的对象
                    if (objectsToHighlight.Count > 0)
                    {
                        selector.Select(objectsToHighlight);
                    }

                    // 给UI线程一些时间来处理高亮
                    System.Windows.Forms.Application.DoEvents();
                }

                LogInfo($"完成批量高亮");
            }
            catch (Exception ex)
            {
                LogError($"高亮对象时出错: {ex.Message}");
            }
        }



        /// <summary>
        /// 使用已有零件数据快速选择构件作为整体（优化版本）
        /// </summary>
        /// <param name="assemblyNumbers">构件编号列表</param>
        /// <param name="allParts">所有零件数据（用于查找主零件）</param>
        /// <returns>是否成功选择构件</returns>
        public bool SelectAssembliesAsWholeOptimized(List<string> assemblyNumbers, List<TeklaModelPart> allParts)
        {
            try
            {
                if (!_model.GetConnectionStatus())
                {
                    LogError("未连接到Tekla模型");
                    return false;
                }

                // 创建ModelObjectSelector对象
                var modelObjectSelector = new Tekla.Structures.Model.UI.ModelObjectSelector();

                // 如果没有指定构件编号，清除当前选择
                if (assemblyNumbers == null || assemblyNumbers.Count == 0)
                {
                    modelObjectSelector.Select(new ArrayList());
                    _model.CommitChanges();
                    return true;
                }

                LogInfo($"开始快速选择 {assemblyNumbers.Count} 个构件作为整体");
                DateTime startTime = DateTime.Now;

                // 使用方案2：从已有数据中查找主零件
                var mainPartIds = allParts
                    .Where(p => assemblyNumbers.Contains(p.AssemblyNumber) && p.IsMainPart)
                    .Select(p => p.ModelObjectId)
                    .Where(id => id.HasValue)
                    .Select(id => id.Value)
                    .Distinct()
                    .ToList();

                LogInfo($"从已有数据中找到 {mainPartIds.Count} 个主零件，耗时 {(DateTime.Now - startTime).TotalMilliseconds:F1} 毫秒");

                if (mainPartIds.Count == 0)
                {
                    LogError("未找到任何主零件，可能是数据问题");
                    return false;
                }

                // 根据主零件ID获取构件对象并选择
                ArrayList assembliesToSelect = new ArrayList();
                int foundCount = 0;

                foreach (var mainPartId in mainPartIds)
                {
                    try
                    {
                        if (_model.SelectModelObject(new Tekla.Structures.Identifier(mainPartId)) is Part mainPart)
                        {
                            var assembly = mainPart.GetAssembly();
                            if (assembly != null)
                            {
                                assembliesToSelect.Add(assembly);
                                foundCount++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogError($"获取主零件构件时发生错误 (ID: {mainPartId}): {ex.Message}");
                    }
                }

                LogInfo($"成功获取 {foundCount} 个构件对象，总耗时 {(DateTime.Now - startTime).TotalSeconds:F1} 秒");

                // 选择找到的构件
                if (assembliesToSelect.Count > 0)
                {
                    try
                    {
                        modelObjectSelector.Select(assembliesToSelect);
                        _model.CommitChanges(); // 确保选择生效
                        LogInfo($"已选择 {assembliesToSelect.Count} 个构件作为整体（优化模式）");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        LogError($"选择构件时发生错误: {ex.Message}");
                        return false;
                    }
                }
                else
                {
                    LogError($"未找到指定的构件");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogError($"快速选择构件作为整体时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 选择构件作为整体（类似Tekla Structures 19.0的构件选择方式）
        /// </summary>
        /// <param name="assemblyNumbers">构件编号列表</param>
        /// <returns>是否成功选择构件</returns>
        public bool SelectAssembliesAsWhole(List<string> assemblyNumbers)
        {
            try
            {
                if (!_model.GetConnectionStatus())
                {
                    LogError("未连接到Tekla模型");
                    return false;
                }

                // 创建ModelObjectSelector对象
                var modelObjectSelector = new Tekla.Structures.Model.UI.ModelObjectSelector();

                // 如果没有指定构件编号，清除当前选择
                if (assemblyNumbers == null || assemblyNumbers.Count == 0)
                {
                    modelObjectSelector.Select(new ArrayList());
                    _model.CommitChanges();
                    return true;
                }

                LogInfo($"开始选择 {assemblyNumbers.Count} 个构件作为整体");
                DateTime startTime = DateTime.Now;

                // 收集需要选择的构件主零件
                ArrayList assembliesToSelect = new ArrayList();
                int foundCount = 0;

                // 遍历所有零件，找到构件的主零件
                ModelObjectEnumerator objectEnum = _model.GetModelObjectSelector().GetAllObjects();
                while (objectEnum.MoveNext())
                {
                    if (objectEnum.Current is Part part)
                    {
                        try
                        {
                            // 获取构件位置编号
                            string assemblyNumber = "";
                            part.GetReportProperty("ASSEMBLY_POS", ref assemblyNumber);

                            // 检查是否是我们要选择的构件
                            if (assemblyNumbers.Contains(assemblyNumber))
                            {
                                // 检查是否是主零件（构件的主要零件）
                                Assembly assembly = part.GetAssembly();
                                if (assembly != null && assembly.GetMainPart() != null)
                                {
                                    ModelObject mainPartObject = assembly.GetMainPart();
                                    if (mainPartObject is Part mainPart && mainPart.Identifier.ID == part.Identifier.ID)
                                    {
                                        // 这是主零件，选择整个构件
                                        assembliesToSelect.Add(assembly);
                                        foundCount++;
                                        LogInfo($"找到构件 {assemblyNumber} 的主零件，ID: {part.Identifier.ID}");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // 忽略单个零件的错误，继续处理其他零件
                            LogError($"处理零件时发生错误: {ex.Message}");
                        }
                    }
                }

                LogInfo($"找到 {foundCount} 个构件，耗时 {(DateTime.Now - startTime).TotalSeconds:F1} 秒");

                // 选择找到的构件
                if (assembliesToSelect.Count > 0)
                {
                    try
                    {
                        modelObjectSelector.Select(assembliesToSelect);
                        _model.CommitChanges(); // 确保选择生效
                        LogInfo($"已选择 {assembliesToSelect.Count} 个构件作为整体（构件整体模式）");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        LogError($"选择构件时发生错误: {ex.Message}");
                        return false;
                    }
                }
                else
                {
                    LogError($"未找到指定的构件");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogError($"选择构件作为整体时发生错误: {ex.Message}");
                return false;
            }
        }


    }
}

